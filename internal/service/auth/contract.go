package auth

import (
	"context"

	"go.uber.org/zap"

	"telescope-be/internal/helper"
	"telescope-be/internal/presentation"
	"telescope-be/internal/repository"
	"telescope-be/internal/validation"
)

type AuthService interface {
	Login(ctx context.Context, request presentation.LoginRequest) (*presentation.LoginResponse, error)
	RefreshToken(ctx context.Context, request presentation.RefreshTokenRequest) (*presentation.LoginResponse, error)
	Logout(ctx context.Context, request presentation.RefreshTokenRequest) error
}

type authService struct {
	repo             *repository.Repository
	jwtHelper        *helper.JWTHelper
	logger           *zap.Logger
	validationHelper *validation.ValidationHelper
}

func NewAuthService(repo *repository.Repository, jwtHelper *helper.J<PERSON>THelper, logger *zap.Logger, validationHelper *validation.ValidationHelper) AuthService {
	return &authService{
		repo:             repo,
		jwtHelper:        jwt<PERSON>el<PERSON>,
		logger:           logger,
		validationHelper: validationHelper,
	}
}
